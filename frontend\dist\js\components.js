// UI组件和工具函数

// 工具方法扩展
Object.assign(LoveSoftwareApp.prototype, {
    // 渲染日记列表
    renderDiaryList() {
        if (this.data.diaries.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon"><i class="fas fa-book"></i></div>
                    <div class="empty-state-title">还没有日记</div>
                    <div class="empty-state-desc">开始写下你们的第一篇日记吧！</div>
                </div>
            `;
        }

        return this.data.diaries.map(diary => `
            <div class="list-item" onclick="loveSoftwareApp.viewDiary('${diary.ID}')">
                <div class="list-item-header">
                    <div>
                        <div class="list-item-title">${diary.Title}</div>
                        <div class="list-item-meta">${this.formatDate(diary.CreatedAt)}</div>
                    </div>
                    <div class="list-item-actions">
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.editDiary('${diary.ID}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.deleteDiary('${diary.ID}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="list-item-content">
                    ${this.truncateText(diary.Content, 100)}
                </div>
                <div class="list-item-footer">
                    <span>最后更新: ${this.formatRelativeTime(diary.UpdatedAt)}</span>
                </div>
            </div>
        `).join('');
    },

    // 渲染纪念日列表
    renderAnniversaryList() {
        if (this.data.anniversaries.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon"><i class="fas fa-calendar-heart"></i></div>
                    <div class="empty-state-title">还没有纪念日</div>
                    <div class="empty-state-desc">添加你们的第一个重要纪念日吧！</div>
                </div>
            `;
        }

        return this.data.anniversaries.map(anniversary => `
            <div class="list-item" onclick="loveSoftwareApp.viewAnniversary('${anniversary.ID}')">
                <div class="list-item-header">
                    <div>
                        <div class="list-item-title">${anniversary.Title}</div>
                        <div class="list-item-meta">${this.formatDate(anniversary.Date)}</div>
                    </div>
                    <div class="list-item-actions">
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.editAnniversary('${anniversary.ID}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.deleteAnniversary('${anniversary.ID}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="list-item-content">
                    ${anniversary.Description}
                </div>
                <div class="list-item-footer">
                    <span>距今: ${this.calculateDaysSince(anniversary.Date)} 天</span>
                </div>
            </div>
        `).join('');
    },

    // 渲染语录列表
    renderQuotesList() {
        if (this.data.quotes.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon"><i class="fas fa-quote-left"></i></div>
                    <div class="empty-state-title">还没有语录</div>
                    <div class="empty-state-desc">收藏你们的第一条爱情语录吧！</div>
                </div>
            `;
        }

        return this.data.quotes.map(quote => `
            <div class="list-item" onclick="loveSoftwareApp.viewQuote('${quote.ID}')">
                <div class="list-item-header">
                    <div>
                        <div class="list-item-title">
                            <i class="fas fa-quote-left"></i>
                            ${quote.Author || '匿名'}
                        </div>
                        <div class="list-item-meta">${this.formatDate(quote.CreatedAt)}</div>
                    </div>
                    <div class="list-item-actions">
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.editQuote('${quote.ID}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.deleteQuote('${quote.ID}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="list-item-content">
                    "${quote.Content}"
                </div>
            </div>
        `).join('');
    },

    // 渲染照片列表
    renderPhotosList() {
        if (this.data.photos.length === 0) {
            return `
                <div class="empty-state">
                    <div class="empty-state-icon"><i class="fas fa-images"></i></div>
                    <div class="empty-state-title">还没有照片</div>
                    <div class="empty-state-desc">上传你们的第一张美好回忆吧！</div>
                </div>
            `;
        }

        return this.data.photos.map(photo => `
            <div class="list-item" onclick="loveSoftwareApp.viewPhoto('${photo.ID}')">
                <div class="list-item-header">
                    <div>
                        <div class="list-item-title">${photo.Filename}</div>
                        <div class="list-item-meta">${this.formatDate(photo.CreatedAt)}</div>
                    </div>
                    <div class="list-item-actions">
                        <button class="icon-btn" onclick="event.stopPropagation(); loveSoftwareApp.deletePhoto('${photo.ID}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="list-item-content">
                    ${photo.Description || '无描述'}
                </div>
                <div class="list-item-footer">
                    <span>文件大小: ${this.formatFileSize(photo.Size || 0)}</span>
                </div>
            </div>
        `).join('');
    },

    // 加载照片页面
    async loadPhotosPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h1><i class="fas fa-images"></i> 相册</h1>
                <p>珍藏美好的回忆瞬间</p>
            </div>
            
            <div class="list-container">
                <div class="list-header">
                    <div class="list-title">
                        <i class="fas fa-images"></i>
                        我的相册
                    </div>
                    <div class="list-actions">
                        <button class="glass-btn primary" onclick="loveSoftwareApp.uploadPhoto()">
                            <i class="fas fa-upload"></i>
                            上传照片
                        </button>
                    </div>
                </div>
                
                <div class="list-grid" id="photosList">
                    ${this.renderPhotosList()}
                </div>
            </div>
        `;
    },

    // 加载统计页面
    async loadStatisticsPage(pageElement) {
        pageElement.innerHTML = `
            <div class="page-header">
                <h1><i class="fas fa-chart-line"></i> 统计</h1>
                <p>查看你们的爱情数据</p>
            </div>
            
            <div class="dashboard-grid">
                <div class="dashboard-card glass-card">
                    <div class="card-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="card-content">
                        <h3>日记总数</h3>
                        <p class="card-number">${this.data.diaries.length}</p>
                        <p class="card-desc">篇日记</p>
                    </div>
                </div>
                
                <div class="dashboard-card glass-card">
                    <div class="card-icon">
                        <i class="fas fa-calendar-heart"></i>
                    </div>
                    <div class="card-content">
                        <h3>纪念日总数</h3>
                        <p class="card-number">${this.data.anniversaries.length}</p>
                        <p class="card-desc">个纪念日</p>
                    </div>
                </div>
                
                <div class="dashboard-card glass-card">
                    <div class="card-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <div class="card-content">
                        <h3>语录总数</h3>
                        <p class="card-number">${this.data.quotes.length}</p>
                        <p class="card-desc">条语录</p>
                    </div>
                </div>
                
                <div class="dashboard-card glass-card">
                    <div class="card-icon">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="card-content">
                        <h3>照片总数</h3>
                        <p class="card-number">${this.data.photos.length}</p>
                        <p class="card-desc">张照片</p>
                    </div>
                </div>
            </div>
        `;
    }
});
