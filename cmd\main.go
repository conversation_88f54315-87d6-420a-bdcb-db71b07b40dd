package main

import (
	"context"
	"embed"
	"log"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"

	"love-software/internal/service"
	"love-software/internal/storage"
)

//go:embed all:frontend/dist
var assets embed.FS

// App struct
type App struct {
	ctx      context.Context
	services *service.ServiceManager
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// OnStartup is called when the app starts up
func (a *App) OnStartup(ctx context.Context) {
	a.ctx = ctx

	log.Println("程序启动...")

	// 初始化存储层
	log.Println("初始化存储层...")
	storageManager, err := storage.NewLocalStorage("./data")
	if err != nil {
		log.Fatalf("初始化存储失败: %v", err)
	}

	// 初始化服务层
	log.Println("初始化服务层...")
	a.services = service.NewServiceManager(storageManager)

	// 初始化测试数据
	log.Println("初始化测试数据...")
	if err := a.services.InitializeTestData(); err != nil {
		log.Printf("初始化测试数据失败: %v", err)
	}

	log.Println("应用初始化完成")
}

func main() {
	// Create an instance of the app structure
	app := NewApp()

	// Create application with options
	err := wails.Run(&options.App{
		Title:  "💕 爱情个人软件",
		Width:  1200,
		Height: 800,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.OnStartup,
		Ctx:              app,
	})

	if err != nil {
		log.Fatalf("启动应用失败: %v", err)
	}
}


