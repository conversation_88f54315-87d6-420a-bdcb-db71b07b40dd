@echo off
echo ========================================
echo 构建 Wails 爱情个人软件
echo ========================================

:: 检查 Wails 是否安装
where wails >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Wails 命令
    echo 请先安装 Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    pause
    exit /b 1
)

:: 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Go 命令
    echo 请先安装 Go: https://golang.org/dl/
    pause
    exit /b 1
)

:: 切换到项目根目录
cd /d "%~dp0\.."

:: 清理之前的构建
echo 清理之前的构建...
if exist "build" rmdir /s /q "build"

:: 下载依赖
echo 下载 Go 依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 下载依赖失败
    pause
    exit /b 1
)

:: 构建应用
echo 构建 Wails 应用...
wails build
if %errorlevel% neq 0 (
    echo 错误: 构建失败
    pause
    exit /b 1
)

echo ========================================
echo 构建完成！
echo 可执行文件位置: build\bin\爱情个人软件.exe
echo ========================================
pause
