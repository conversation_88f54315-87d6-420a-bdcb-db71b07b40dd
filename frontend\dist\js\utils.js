// 工具函数和CRUD操作

// 扩展应用类的工具方法
Object.assign(LoveSoftwareApp.prototype, {
    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    },

    // 格式化相对时间
    formatRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString('zh-CN');
    },

    // 计算距今天数
    calculateDaysSince(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        return Math.floor(diff / 86400000);
    },

    // 截断文本
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 更新状态栏
    updateStatusBar(message) {
        this.updateElement('statusText', message);
    },

    // CRUD操作方法
    createDiary() {
        console.log('创建日记');
        this.showModal('创建日记', this.getDiaryForm(), (formData) => {
            this.saveDiary(formData);
        });
    },

    viewDiary(id) {
        console.log('查看日记:', id);
        const diary = this.data.diaries.find(d => d.ID === id);
        if (diary) {
            this.showModal('查看日记', this.getDiaryView(diary));
        }
    },

    editDiary(id) {
        console.log('编辑日记:', id);
        const diary = this.data.diaries.find(d => d.ID === id);
        if (diary) {
            this.showModal('编辑日记', this.getDiaryForm(diary), (formData) => {
                this.updateDiary(id, formData);
            });
        }
    },

    async deleteDiary(id) {
        console.log('删除日记:', id);
        if (confirm('确定要删除这篇日记吗？')) {
            try {
                if (window.go && window.go.main && window.go.main.App && window.go.main.App.DeleteDiary) {
                    await window.go.main.App.DeleteDiary(id);
                }
                // 重新加载数据
                await this.loadInitialData();
                this.showNotification('日记删除成功', 'success');
            } catch (error) {
                console.error('删除日记失败:', error);
                this.showNotification('删除日记失败', 'error');
            }
        }
    },

    createAnniversary() {
        console.log('创建纪念日');
        this.showModal('创建纪念日', this.getAnniversaryForm(), (formData) => {
            this.saveAnniversary(formData);
        });
    },

    viewAnniversary(id) {
        console.log('查看纪念日:', id);
        const anniversary = this.data.anniversaries.find(a => a.ID === id);
        if (anniversary) {
            this.showModal('查看纪念日', this.getAnniversaryView(anniversary));
        }
    },

    editAnniversary(id) {
        console.log('编辑纪念日:', id);
        const anniversary = this.data.anniversaries.find(a => a.ID === id);
        if (anniversary) {
            this.showModal('编辑纪念日', this.getAnniversaryForm(anniversary), (formData) => {
                this.updateAnniversary(id, formData);
            });
        }
    },

    async deleteAnniversary(id) {
        console.log('删除纪念日:', id);
        if (confirm('确定要删除这个纪念日吗？')) {
            try {
                if (window.go && window.go.main && window.go.main.App && window.go.main.App.DeleteAnniversary) {
                    await window.go.main.App.DeleteAnniversary(id);
                }
                // 重新加载数据
                await this.loadInitialData();
                this.showNotification('纪念日删除成功', 'success');
            } catch (error) {
                console.error('删除纪念日失败:', error);
                this.showNotification('删除纪念日失败', 'error');
            }
        }
    },

    createQuote() {
        console.log('创建语录');
        this.showModal('创建语录', this.getQuoteForm(), (formData) => {
            this.saveQuote(formData);
        });
    },

    viewQuote(id) {
        console.log('查看语录:', id);
        const quote = this.data.quotes.find(q => q.ID === id);
        if (quote) {
            this.showModal('查看语录', this.getQuoteView(quote));
        }
    },

    editQuote(id) {
        console.log('编辑语录:', id);
        const quote = this.data.quotes.find(q => q.ID === id);
        if (quote) {
            this.showModal('编辑语录', this.getQuoteForm(quote), (formData) => {
                this.updateQuote(id, formData);
            });
        }
    },

    async deleteQuote(id) {
        console.log('删除语录:', id);
        if (confirm('确定要删除这条语录吗？')) {
            try {
                if (window.go && window.go.main && window.go.main.App && window.go.main.App.DeleteQuote) {
                    await window.go.main.App.DeleteQuote(id);
                }
                // 重新加载数据
                await this.loadInitialData();
                this.showNotification('语录删除成功', 'success');
            } catch (error) {
                console.error('删除语录失败:', error);
                this.showNotification('删除语录失败', 'error');
            }
        }
    },

    uploadPhoto() {
        console.log('上传照片');
        // TODO: 实现文件上传功能
        this.showNotification('照片上传功能开发中...', 'info');
    },

    viewPhoto(id) {
        console.log('查看照片:', id);
        const photo = this.data.photos.find(p => p.ID === id);
        if (photo) {
            this.showModal('查看照片', this.getPhotoView(photo));
        }
    },

    async deletePhoto(id) {
        console.log('删除照片:', id);
        if (confirm('确定要删除这张照片吗？')) {
            try {
                if (window.go && window.go.main && window.go.main.App && window.go.main.App.DeletePhoto) {
                    await window.go.main.App.DeletePhoto(id);
                }
                // 重新加载数据
                await this.loadInitialData();
                this.showNotification('照片删除成功', 'success');
            } catch (error) {
                console.error('删除照片失败:', error);
                this.showNotification('删除照片失败', 'error');
            }
        }
    },

    // 保存操作
    async saveDiary(formData) {
        try {
            this.showLoading('保存日记中...');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.CreateDiary) {
                await window.go.main.App.CreateDiary(formData.title, formData.content);
            }
            // 重新加载数据
            await this.loadInitialData();
            this.hideModal();
            this.showNotification('日记保存成功', 'success');
        } catch (error) {
            console.error('保存日记失败:', error);
            this.showNotification('保存日记失败', 'error');
        } finally {
            this.hideLoading();
        }
    },

    async updateDiary(id, formData) {
        try {
            this.showLoading('更新日记中...');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.UpdateDiary) {
                await window.go.main.App.UpdateDiary(id, formData.title, formData.content);
            }
            // 重新加载数据
            await this.loadInitialData();
            this.hideModal();
            this.showNotification('日记更新成功', 'success');
        } catch (error) {
            console.error('更新日记失败:', error);
            this.showNotification('更新日记失败', 'error');
        } finally {
            this.hideLoading();
        }
    },

    async saveAnniversary(formData) {
        try {
            this.showLoading('保存纪念日中...');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.CreateAnniversary) {
                await window.go.main.App.CreateAnniversary(formData.title, formData.description, new Date(formData.date));
            }
            // 重新加载数据
            await this.loadInitialData();
            this.hideModal();
            this.showNotification('纪念日保存成功', 'success');
        } catch (error) {
            console.error('保存纪念日失败:', error);
            this.showNotification('保存纪念日失败', 'error');
        } finally {
            this.hideLoading();
        }
    },

    async updateAnniversary(id, formData) {
        try {
            this.showLoading('更新纪念日中...');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.UpdateAnniversary) {
                await window.go.main.App.UpdateAnniversary(id, formData.title, formData.description, new Date(formData.date));
            }
            // 重新加载数据
            await this.loadInitialData();
            this.hideModal();
            this.showNotification('纪念日更新成功', 'success');
        } catch (error) {
            console.error('更新纪念日失败:', error);
            this.showNotification('更新纪念日失败', 'error');
        } finally {
            this.hideLoading();
        }
    },

    async saveQuote(formData) {
        try {
            this.showLoading('保存语录中...');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.CreateQuote) {
                await window.go.main.App.CreateQuote(formData.content, formData.author);
            }
            // 重新加载数据
            await this.loadInitialData();
            this.hideModal();
            this.showNotification('语录保存成功', 'success');
        } catch (error) {
            console.error('保存语录失败:', error);
            this.showNotification('保存语录失败', 'error');
        } finally {
            this.hideLoading();
        }
    },

    async updateQuote(id, formData) {
        try {
            this.showLoading('更新语录中...');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.UpdateQuote) {
                await window.go.main.App.UpdateQuote(id, formData.content, formData.author);
            }
            // 重新加载数据
            await this.loadInitialData();
            this.hideModal();
            this.showNotification('语录更新成功', 'success');
        } catch (error) {
            console.error('更新语录失败:', error);
            this.showNotification('更新语录失败', 'error');
        } finally {
            this.hideLoading();
        }
    }
});
