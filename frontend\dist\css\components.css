/* 组件样式 */

/* 仪表板网格 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-2xl);
}

.dashboard-card {
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.dashboard-card:hover {
    transform: translateY(-5px) scale(1.02);
}

.dashboard-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.3);
}

.dashboard-card .card-icon i {
    font-size: var(--font-size-2xl);
    color: var(--text-white);
}

.dashboard-card .card-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-white);
    margin-bottom: var(--spacing-xs);
}

.dashboard-card .card-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    line-height: 1;
}

.dashboard-card .card-desc {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin: 0;
    opacity: 0.8;
}

/* 最近活动 */
.recent-activities {
    padding: var(--spacing-xl);
    border-radius: var(--radius-xl);
}

.recent-activities h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-white);
    margin-bottom: var(--spacing-lg);
}

.recent-activities h3 i {
    color: var(--primary-color);
}

.activities-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.activity-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--secondary-color), #7dd3fc);
    color: var(--text-primary);
    font-weight: 600;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    color: var(--text-white);
    margin-bottom: 2px;
}

.activity-desc {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    opacity: 0.8;
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--text-light);
    opacity: 0.6;
}

/* 列表组件 */
.list-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
}

.list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.list-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--text-white);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.list-title i {
    color: var(--primary-color);
}

.list-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.list-grid {
    display: grid;
    gap: var(--spacing-lg);
}

.list-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.list-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.list-item-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.list-item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-white);
    margin-bottom: var(--spacing-xs);
}

.list-item-meta {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    opacity: 0.8;
}

.list-item-actions {
    display: flex;
    gap: var(--spacing-xs);
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.list-item:hover .list-item-actions {
    opacity: 1;
}

.list-item-content {
    color: var(--text-white);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.list-item-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--text-light);
    opacity: 0.8;
}

/* 表单组件 */
.form-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    color: var(--text-white);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.form-label.required::after {
    content: ' *';
    color: var(--primary-color);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片组件 */
.card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-white);
    margin: 0;
}

.card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-light);
    margin: var(--spacing-xs) 0 0 0;
    opacity: 0.8;
}

.card-body {
    padding: var(--spacing-lg);
    color: var(--text-white);
    line-height: 1.6;
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
}

/* 标签组件 */
.tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tag.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-color: var(--primary-light);
}

.tag.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #7dd3fc);
    color: var(--text-primary);
    border-color: var(--secondary-color);
}

.tag.accent {
    background: linear-gradient(135deg, var(--accent-color), #fbbf24);
    color: var(--text-primary);
    border-color: var(--accent-color);
}

/* 分页组件 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-xl);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
}

.pagination-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.pagination-btn.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-color: var(--primary-light);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn:disabled:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

/* 空状态组件 */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-white);
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--text-light);
    opacity: 0.5;
    margin-bottom: var(--spacing-lg);
}

.empty-state-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.empty-state-desc {
    font-size: var(--font-size-base);
    color: var(--text-light);
    opacity: 0.8;
    margin-bottom: var(--spacing-xl);
}

/* 响应式组件 */
@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .list-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .list-actions {
        justify-content: center;
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}
