// 主应用程序逻辑
class LoveSoftwareApp {
    constructor() {
        this.currentPage = 'dashboard';
        this.data = {
            diaries: [],
            anniversaries: [],
            quotes: [],
            photos: [],
            statistics: {}
        };
        this.init();
    }

    // 初始化应用
    async init() {
        console.log('初始化爱情个人软件...');
        
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
        } else {
            this.onDOMReady();
        }
    }

    // DOM准备就绪
    async onDOMReady() {
        console.log('DOM准备就绪');
        
        // 初始化事件监听器
        this.initEventListeners();
        
        // 初始化时间显示
        this.initTimeDisplay();
        
        // 加载初始数据
        await this.loadInitialData();
        
        // 隐藏加载提示
        this.hideLoading();
        
        // 显示欢迎消息
        this.showWelcomeMessage();
        
        console.log('应用初始化完成');
    }

    // 初始化事件监听器
    initEventListeners() {
        // 导航菜单点击事件
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const page = e.currentTarget.dataset.page;
                if (page) {
                    this.navigateToPage(page);
                }
            });
        });

        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        // 设置按钮
        const settingsBtn = document.getElementById('settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettings();
            });
        }

        // 通知按钮
        const notificationBtn = document.getElementById('notificationBtn');
        if (notificationBtn) {
            notificationBtn.addEventListener('click', () => {
                this.showNotifications();
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    // 初始化时间显示
    initTimeDisplay() {
        const updateTime = () => {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            const timeElement = document.getElementById('currentTime');
            if (timeElement) {
                timeElement.textContent = timeString;
            }
        };

        updateTime();
        setInterval(updateTime, 1000);
    }

    // 加载初始数据
    async loadInitialData() {
        try {
            this.showLoading('加载数据中...');
            
            // 并行加载所有数据
            const [diaries, anniversaries, quotes, photos, statistics] = await Promise.all([
                this.loadDiaries(),
                this.loadAnniversaries(),
                this.loadQuotes(),
                this.loadPhotos(),
                this.loadStatistics()
            ]);

            this.data.diaries = diaries;
            this.data.anniversaries = anniversaries;
            this.data.quotes = quotes;
            this.data.photos = photos;
            this.data.statistics = statistics;

            // 更新UI计数
            this.updateCounts();
            
            // 更新仪表板
            this.updateDashboard();
            
            console.log('数据加载完成', this.data);
            
        } catch (error) {
            console.error('加载数据失败:', error);
            this.showNotification('加载数据失败，请检查网络连接', 'error');
        }
    }

    // 加载日记数据
    async loadDiaries() {
        try {
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetDiaries) {
                return await window.go.main.App.GetDiaries();
            }
            return [];
        } catch (error) {
            console.error('加载日记失败:', error);
            return [];
        }
    }

    // 加载纪念日数据
    async loadAnniversaries() {
        try {
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetAnniversaries) {
                return await window.go.main.App.GetAnniversaries();
            }
            return [];
        } catch (error) {
            console.error('加载纪念日失败:', error);
            return [];
        }
    }

    // 加载语录数据
    async loadQuotes() {
        try {
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetQuotes) {
                return await window.go.main.App.GetQuotes();
            }
            return [];
        } catch (error) {
            console.error('加载语录失败:', error);
            return [];
        }
    }

    // 加载照片数据
    async loadPhotos() {
        try {
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetPhotos) {
                return await window.go.main.App.GetPhotos();
            }
            return [];
        } catch (error) {
            console.error('加载照片失败:', error);
            return [];
        }
    }

    // 加载统计数据
    async loadStatistics() {
        try {
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.GetStatistics) {
                return await window.go.main.App.GetStatistics();
            }
            return {};
        } catch (error) {
            console.error('加载统计数据失败:', error);
            return {};
        }
    }

    // 更新计数显示
    updateCounts() {
        const counts = {
            diary: this.data.diaries.length,
            anniversary: this.data.anniversaries.length,
            quotes: this.data.quotes.length,
            photos: this.data.photos.length
        };

        // 更新导航栏徽章
        this.updateElement('diaryCount', counts.diary);
        this.updateElement('anniversaryCount', counts.anniversary);
        this.updateElement('quotesCount', counts.quotes);
        this.updateElement('photosCount', counts.photos);

        // 更新仪表板计数
        this.updateElement('dashboardDiaryCount', counts.diary);
        this.updateElement('dashboardAnniversaryCount', counts.anniversary);
        this.updateElement('dashboardQuotesCount', counts.quotes);
        this.updateElement('dashboardPhotosCount', counts.photos);
    }

    // 更新仪表板
    updateDashboard() {
        // 更新最近活动
        this.updateRecentActivities();
        
        // 更新随机语录
        this.updateRandomQuote();
    }

    // 更新最近活动
    updateRecentActivities() {
        const activities = [];
        
        // 添加最近的日记
        this.data.diaries.slice(0, 3).forEach(diary => {
            activities.push({
                type: 'diary',
                icon: '📖',
                title: diary.Title,
                desc: '写了一篇日记',
                time: this.formatRelativeTime(diary.CreatedAt)
            });
        });

        // 添加最近的纪念日
        this.data.anniversaries.slice(0, 2).forEach(anniversary => {
            activities.push({
                type: 'anniversary',
                icon: '💕',
                title: anniversary.Title,
                desc: '添加了纪念日',
                time: this.formatRelativeTime(anniversary.CreatedAt)
            });
        });

        // 按时间排序
        activities.sort((a, b) => new Date(b.time) - new Date(a.time));

        // 渲染活动列表
        const container = document.getElementById('recentActivities');
        if (container) {
            if (activities.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">💝</div>
                        <div class="empty-state-title">还没有活动记录</div>
                        <div class="empty-state-desc">开始记录你们的美好时光吧！</div>
                    </div>
                `;
            } else {
                container.innerHTML = activities.map(activity => `
                    <div class="activity-item">
                        <div class="activity-icon">${activity.icon}</div>
                        <div class="activity-content">
                            <div class="activity-title">${activity.title}</div>
                            <div class="activity-desc">${activity.desc}</div>
                        </div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                `).join('');
            }
        }
    }

    // 更新随机语录
    updateRandomQuote() {
        const quotes = [
            '记录每一个美好瞬间',
            '爱情是生活最好的提神剂',
            '时间会记住我们的爱情',
            '每一天都是新的开始',
            '爱让生活变得更美好',
            ...this.data.quotes.map(q => q.Content)
        ];

        const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
        this.updateElement('randomQuote', randomQuote);
    }

    // 页面导航
    navigateToPage(pageName) {
        console.log('导航到页面:', pageName);
        
        // 更新当前页面
        this.currentPage = pageName;
        
        // 更新导航菜单状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
            if (item.dataset.page === pageName) {
                item.classList.add('active');
            }
        });

        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
            
            // 加载页面内容
            this.loadPageContent(pageName);
        }

        // 更新状态栏
        this.updateStatusBar(`当前页面: ${this.getPageTitle(pageName)}`);
    }

    // 获取页面标题
    getPageTitle(pageName) {
        const titles = {
            dashboard: '首页',
            diary: '日记本',
            anniversary: '纪念日',
            quotes: '爱情语录',
            photos: '相册',
            statistics: '统计'
        };
        return titles[pageName] || pageName;
    }

    // 加载页面内容
    async loadPageContent(pageName) {
        const pageElement = document.getElementById(`${pageName}-page`);
        if (!pageElement) return;

        try {
            this.showLoading(`加载${this.getPageTitle(pageName)}...`);
            
            // 根据页面类型加载不同内容
            switch (pageName) {
                case 'diary':
                    await this.loadDiaryPage(pageElement);
                    break;
                case 'anniversary':
                    await this.loadAnniversaryPage(pageElement);
                    break;
                case 'quotes':
                    await this.loadQuotesPage(pageElement);
                    break;
                case 'photos':
                    await this.loadPhotosPage(pageElement);
                    break;
                case 'statistics':
                    await this.loadStatisticsPage(pageElement);
                    break;
                default:
                    break;
            }
            
            this.hideLoading();
            
        } catch (error) {
            console.error(`加载${pageName}页面失败:`, error);
            this.showNotification(`加载${this.getPageTitle(pageName)}失败`, 'error');
            this.hideLoading();
        }
    }

    // 工具方法
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    formatRelativeTime(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;
        
        return date.toLocaleDateString('zh-CN');
    }

    showLoading(message = '加载中...') {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('hidden');
            const text = overlay.querySelector('p');
            if (text) text.textContent = message;
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    updateStatusBar(message) {
        this.updateElement('statusText', message);
    }

    showWelcomeMessage() {
        this.showNotification('欢迎使用爱情个人软件！💕', 'success');
    }

    showNotification(message, type = 'info') {
        console.log(`通知 [${type}]:`, message);
        // TODO: 实现通知UI
    }

    showSettings() {
        console.log('显示设置');
        // TODO: 实现设置界面
    }

    showNotifications() {
        console.log('显示通知');
        // TODO: 实现通知界面
    }

    handleSearch(query) {
        console.log('搜索:', query);
        // TODO: 实现搜索功能
    }

    handleKeyboardShortcuts(e) {
        // Ctrl**** 快速导航
        if (e.ctrlKey && e.key >= '1' && e.key <= '6') {
            e.preventDefault();
            const pages = ['dashboard', 'diary', 'anniversary', 'quotes', 'photos', 'statistics'];
            const pageIndex = parseInt(e.key) - 1;
            if (pages[pageIndex]) {
                this.navigateToPage(pages[pageIndex]);
            }
        }
    }
}

// 创建全局应用实例
window.loveSoftwareApp = new LoveSoftwareApp();
