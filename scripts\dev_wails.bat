@echo off
echo ========================================
echo 启动 Wails 开发服务器
echo ========================================

:: 检查 Wails 是否安装
where wails >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Wails 命令
    echo 请先安装 Wails: go install github.com/wailsapp/wails/v2/cmd/wails@latest
    pause
    exit /b 1
)

:: 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Go 命令
    echo 请先安装 Go: https://golang.org/dl/
    pause
    exit /b 1
)

:: 切换到项目根目录
cd /d "%~dp0\.."

:: 下载依赖
echo 下载 Go 依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 下载依赖失败
    pause
    exit /b 1
)

:: 启动开发服务器
echo 启动开发服务器...
echo 注意: 首次启动可能需要较长时间
wails dev
if %errorlevel% neq 0 (
    echo 错误: 启动开发服务器失败
    pause
    exit /b 1
)

pause
