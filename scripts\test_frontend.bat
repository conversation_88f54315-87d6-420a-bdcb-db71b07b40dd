@echo off
echo ========================================
echo 测试前端界面
echo ========================================

:: 切换到项目根目录
cd /d "%~dp0\.."

:: 检查前端文件是否存在
if not exist "frontend\dist\index.html" (
    echo 错误: 前端文件不存在
    echo 请确保 frontend\dist\index.html 文件存在
    pause
    exit /b 1
)

:: 启动简单的HTTP服务器来测试前端
echo 启动本地HTTP服务器...
echo 浏览器将自动打开 http://localhost:8080
echo 按 Ctrl+C 停止服务器

:: 尝试使用Python启动服务器
where python >nul 2>nul
if %errorlevel% equ 0 (
    cd frontend\dist
    start http://localhost:8080
    python -m http.server 8080
    goto :end
)

:: 尝试使用Python3启动服务器
where python3 >nul 2>nul
if %errorlevel% equ 0 (
    cd frontend\dist
    start http://localhost:8080
    python3 -m http.server 8080
    goto :end
)

:: 如果没有Python，尝试使用Node.js
where node >nul 2>nul
if %errorlevel% equ 0 (
    where npx >nul 2>nul
    if %errorlevel% equ 0 (
        cd frontend\dist
        start http://localhost:8080
        npx http-server -p 8080
        goto :end
    )
)

:: 如果都没有，提示用户
echo 错误: 未找到 Python 或 Node.js
echo 请安装 Python 或 Node.js 来启动本地服务器
echo 或者直接在浏览器中打开 frontend\dist\index.html 文件
pause

:end
echo 服务器已停止
pause
