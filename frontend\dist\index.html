<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💕 爱情个人软件</title>
    
    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/glassmorphism.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- 背景装饰 -->
    <div class="background-decoration">
        <div class="floating-hearts">
            <div class="heart heart-1">💕</div>
            <div class="heart heart-2">💖</div>
            <div class="heart heart-3">💝</div>
            <div class="heart heart-4">💗</div>
            <div class="heart heart-5">💘</div>
        </div>
        <div class="gradient-orbs">
            <div class="orb orb-1"></div>
            <div class="orb orb-2"></div>
            <div class="orb orb-3"></div>
        </div>
    </div>

    <!-- 主应用容器 -->
    <div id="app" class="app-container">
        <!-- 顶部导航栏 -->
        <header class="app-header glass-card">
            <div class="header-left">
                <div class="app-logo">
                    <i class="fas fa-heart"></i>
                    <span>爱情个人软件</span>
                </div>
            </div>
            <div class="header-center">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" placeholder="搜索日记、纪念日、语录..." id="searchInput">
                </div>
            </div>
            <div class="header-right">
                <button class="icon-btn" id="settingsBtn" title="设置">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="icon-btn" id="notificationBtn" title="通知">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="app-main">
            <!-- 侧边导航 -->
            <nav class="sidebar glass-card">
                <div class="nav-menu">
                    <div class="nav-item active" data-page="dashboard">
                        <i class="fas fa-home"></i>
                        <span>首页</span>
                    </div>
                    <div class="nav-item" data-page="diary">
                        <i class="fas fa-book"></i>
                        <span>日记本</span>
                        <span class="nav-badge" id="diaryCount">0</span>
                    </div>
                    <div class="nav-item" data-page="anniversary">
                        <i class="fas fa-calendar-heart"></i>
                        <span>纪念日</span>
                        <span class="nav-badge" id="anniversaryCount">0</span>
                    </div>
                    <div class="nav-item" data-page="quotes">
                        <i class="fas fa-quote-left"></i>
                        <span>爱情语录</span>
                        <span class="nav-badge" id="quotesCount">0</span>
                    </div>
                    <div class="nav-item" data-page="photos">
                        <i class="fas fa-images"></i>
                        <span>相册</span>
                        <span class="nav-badge" id="photosCount">0</span>
                    </div>
                    <div class="nav-item" data-page="statistics">
                        <i class="fas fa-chart-line"></i>
                        <span>统计</span>
                    </div>
                </div>
                
                <div class="nav-footer">
                    <div class="love-quote">
                        <i class="fas fa-heart"></i>
                        <p id="randomQuote">记录每一个美好瞬间</p>
                    </div>
                </div>
            </nav>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 首页 -->
                <div id="dashboard-page" class="page active">
                    <div class="page-header">
                        <h1>💕 欢迎回来</h1>
                        <p>今天也要记录美好的一天哦~</p>
                    </div>
                    
                    <div class="dashboard-grid">
                        <div class="dashboard-card glass-card">
                            <div class="card-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="card-content">
                                <h3>日记</h3>
                                <p class="card-number" id="dashboardDiaryCount">0</p>
                                <p class="card-desc">篇日记</p>
                            </div>
                        </div>
                        
                        <div class="dashboard-card glass-card">
                            <div class="card-icon">
                                <i class="fas fa-calendar-heart"></i>
                            </div>
                            <div class="card-content">
                                <h3>纪念日</h3>
                                <p class="card-number" id="dashboardAnniversaryCount">0</p>
                                <p class="card-desc">个纪念日</p>
                            </div>
                        </div>
                        
                        <div class="dashboard-card glass-card">
                            <div class="card-icon">
                                <i class="fas fa-quote-left"></i>
                            </div>
                            <div class="card-content">
                                <h3>语录</h3>
                                <p class="card-number" id="dashboardQuotesCount">0</p>
                                <p class="card-desc">条语录</p>
                            </div>
                        </div>
                        
                        <div class="dashboard-card glass-card">
                            <div class="card-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="card-content">
                                <h3>照片</h3>
                                <p class="card-number" id="dashboardPhotosCount">0</p>
                                <p class="card-desc">张照片</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="recent-activities glass-card">
                        <h3><i class="fas fa-clock"></i> 最近活动</h3>
                        <div id="recentActivities" class="activities-list">
                            <!-- 动态加载最近活动 -->
                        </div>
                    </div>
                </div>

                <!-- 其他页面将通过JavaScript动态加载 -->
                <div id="diary-page" class="page"></div>
                <div id="anniversary-page" class="page"></div>
                <div id="quotes-page" class="page"></div>
                <div id="photos-page" class="page"></div>
                <div id="statistics-page" class="page"></div>
            </div>
        </main>

        <!-- 状态栏 -->
        <footer class="app-footer glass-card">
            <div class="footer-left">
                <span id="statusText">就绪</span>
            </div>
            <div class="footer-center">
                <span>💝 记录每一个美好瞬间</span>
            </div>
            <div class="footer-right">
                <span id="currentTime"></span>
            </div>
        </footer>
    </div>

    <!-- 加载提示 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>加载中...</p>
        </div>
    </div>

    <!-- 模态框容器 -->
    <div id="modalContainer"></div>

    <!-- JavaScript -->
    <script src="js/wails.js"></script>
    <script src="js/app.js"></script>
    <script src="js/components.js"></script>
    <script src="js/pages.js"></script>
    <script src="js/utils.js"></script>
</body>
</html>
