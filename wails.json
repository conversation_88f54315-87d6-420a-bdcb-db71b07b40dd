{"$schema": "https://wails.io/schemas/config.v2.json", "name": "love-software", "outputfilename": "爱情个人软件", "frontend:install": "", "frontend:build": "", "frontend:dev:watcher": "", "frontend:dev:serverUrl": "", "frontend:dev:build": "", "frontend:dev:install": "", "frontend:dev:shell": "", "author": {"name": "Love Software Team", "email": "<EMAIL>"}, "info": {"companyName": "Love Software", "productName": "爱情个人软件", "productVersion": "1.0.0", "copyright": "Copyright © 2024 Love Software Team", "comments": "记录每一个美好瞬间的爱情个人软件"}, "wailsjsdir": "./frontend/dist", "assetdir": "./frontend/dist", "reloaddirs": "./frontend/dist,./cmd", "build": {"compiler": "go", "ldflags": ["-w", "-s"]}, "nsisType": "multiple", "obfuscated": false, "garbleargs": ""}