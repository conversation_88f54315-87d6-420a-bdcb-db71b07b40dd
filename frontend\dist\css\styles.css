/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 根变量定义 */
:root {
    /* 主色调 */
    --primary-color: #ff6b9d;
    --primary-light: #ff8fb3;
    --primary-dark: #e55a87;
    --secondary-color: #a8e6cf;
    --accent-color: #ffd93d;
    
    /* 背景色 */
    --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --bg-tertiary: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    
    /* 文字颜色 */
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-light: #a0aec0;
    --text-white: #ffffff;
    
    /* 玻璃效果 */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 圆角 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* 过渡动画 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 基础样式 */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    overflow-x: hidden;
}

/* 背景装饰 */
.background-decoration {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-hearts {
    position: absolute;
    width: 100%;
    height: 100%;
}

.heart {
    position: absolute;
    font-size: 2rem;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.heart-1 { top: 10%; left: 10%; animation-delay: 0s; }
.heart-2 { top: 20%; right: 15%; animation-delay: 1s; }
.heart-3 { bottom: 30%; left: 20%; animation-delay: 2s; }
.heart-4 { bottom: 20%; right: 10%; animation-delay: 3s; }
.heart-5 { top: 50%; left: 50%; animation-delay: 4s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

.gradient-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
}

.orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.3;
    animation: drift 8s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #ff6b9d, #a8e6cf);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.orb-3 {
    width: 250px;
    height: 250px;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

@keyframes drift {
    0%, 100% { transform: translate(0, 0) scale(1); }
    33% { transform: translate(30px, -30px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
}

/* 应用容器 */
.app-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

/* 顶部导航栏 */
.app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-xl);
    margin: var(--spacing-md);
    margin-bottom: 0;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
}

.header-left .app-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-white);
}

.header-left .app-logo i {
    color: var(--primary-color);
    font-size: var(--font-size-xl);
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 var(--spacing-xl);
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: var(--spacing-md);
    color: var(--text-light);
    z-index: 1;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    font-size: var(--font-size-sm);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.search-box input::placeholder {
    color: var(--text-light);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-white);
    cursor: pointer;
    transition: all var(--transition-normal);
    backdrop-filter: blur(10px);
}

.icon-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* 主内容区域 */
.app-main {
    display: flex;
    flex: 1;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-xl);
    padding-top: var(--spacing-md);
}

/* 侧边导航 */
.sidebar {
    width: 280px;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    display: flex;
    flex-direction: column;
    height: fit-content;
}

.nav-menu {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    color: var(--text-white);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.nav-item.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.nav-item i {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.nav-item span {
    font-weight: 500;
}

.nav-badge {
    margin-left: auto;
    background: var(--accent-color);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.nav-footer {
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--glass-border);
}

.love-quote {
    text-align: center;
    color: var(--text-white);
}

.love-quote i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
}

.love-quote p {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    font-style: italic;
}

/* 内容区域 */
.content-area {
    flex: 1;
    position: relative;
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 页面头部 */
.page-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.page-header h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-white);
    margin-bottom: var(--spacing-sm);
}

.page-header p {
    font-size: var(--font-size-lg);
    color: var(--text-light);
    opacity: 0.9;
}

/* 底部状态栏 */
.app-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) var(--spacing-xl);
    margin: var(--spacing-md);
    margin-top: 0;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    font-size: var(--font-size-sm);
    color: var(--text-white);
}

.footer-center {
    font-style: italic;
    opacity: 0.8;
}

/* 加载提示 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    text-align: center;
    color: var(--text-white);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .app-main {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        order: 2;
    }
    
    .content-area {
        order: 1;
    }
}

@media (max-width: 768px) {
    .app-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .header-center {
        margin: 0;
        max-width: 100%;
    }
    
    .nav-menu {
        flex-direction: row;
        overflow-x: auto;
        gap: var(--spacing-sm);
    }
    
    .nav-item {
        flex-shrink: 0;
        min-width: 120px;
        justify-content: center;
    }
    
    .nav-item span:not(.nav-badge) {
        display: none;
    }
    
    .nav-footer {
        display: none;
    }
}
