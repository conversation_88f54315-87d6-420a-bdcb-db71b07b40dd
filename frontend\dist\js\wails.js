// Wails Runtime API Mock for Development
// This file provides a mock implementation of Wails runtime for development purposes

window.wails = window.wails || {};

// Mock runtime object
window.wails.runtime = {
    // Environment info
    Environment: {
        buildType: 'dev',
        platform: 'windows',
        arch: 'amd64'
    },
    
    // Window management
    WindowReload: () => {
        console.log('WindowReload called');
        window.location.reload();
    },
    
    WindowReloadApp: () => {
        console.log('WindowReloadApp called');
        window.location.reload();
    },
    
    WindowSetAlwaysOnTop: (onTop) => {
        console.log('WindowSetAlwaysOnTop called:', onTop);
    },
    
    WindowShow: () => {
        console.log('WindowShow called');
    },
    
    WindowHide: () => {
        console.log('WindowHide called');
    },
    
    WindowIsMaximised: () => {
        console.log('WindowIsMaximised called');
        return Promise.resolve(false);
    },
    
    WindowMaximise: () => {
        console.log('WindowMaximise called');
    },
    
    WindowUnmaximise: () => {
        console.log('WindowUnmaximise called');
    },
    
    WindowMinimise: () => {
        console.log('WindowMinimise called');
    },
    
    WindowUnminimise: () => {
        console.log('WindowUnminimise called');
    },
    
    WindowCenter: () => {
        console.log('WindowCenter called');
    },
    
    WindowSetPosition: (x, y) => {
        console.log('WindowSetPosition called:', x, y);
    },
    
    WindowGetPosition: () => {
        console.log('WindowGetPosition called');
        return Promise.resolve({ x: 100, y: 100 });
    },
    
    WindowSetSize: (width, height) => {
        console.log('WindowSetSize called:', width, height);
    },
    
    WindowGetSize: () => {
        console.log('WindowGetSize called');
        return Promise.resolve({ width: 1200, height: 800 });
    },
    
    WindowSetTitle: (title) => {
        console.log('WindowSetTitle called:', title);
        document.title = title;
    },
    
    WindowFullscreen: () => {
        console.log('WindowFullscreen called');
    },
    
    WindowUnfullscreen: () => {
        console.log('WindowUnfullscreen called');
    },
    
    WindowIsFullscreen: () => {
        console.log('WindowIsFullscreen called');
        return Promise.resolve(false);
    },
    
    // System
    BrowserOpenURL: (url) => {
        console.log('BrowserOpenURL called:', url);
        window.open(url, '_blank');
    },
    
    // Events
    EventsOn: (eventName, callback) => {
        console.log('EventsOn called:', eventName);
        // Store event listeners for mock purposes
        window.wails._eventListeners = window.wails._eventListeners || {};
        window.wails._eventListeners[eventName] = window.wails._eventListeners[eventName] || [];
        window.wails._eventListeners[eventName].push(callback);
    },
    
    EventsOff: (eventName, callback) => {
        console.log('EventsOff called:', eventName);
        if (window.wails._eventListeners && window.wails._eventListeners[eventName]) {
            const index = window.wails._eventListeners[eventName].indexOf(callback);
            if (index > -1) {
                window.wails._eventListeners[eventName].splice(index, 1);
            }
        }
    },
    
    EventsEmit: (eventName, data) => {
        console.log('EventsEmit called:', eventName, data);
        if (window.wails._eventListeners && window.wails._eventListeners[eventName]) {
            window.wails._eventListeners[eventName].forEach(callback => {
                callback(data);
            });
        }
    },
    
    // Clipboard
    ClipboardGetText: () => {
        console.log('ClipboardGetText called');
        return navigator.clipboard.readText().catch(() => '');
    },
    
    ClipboardSetText: (text) => {
        console.log('ClipboardSetText called:', text);
        return navigator.clipboard.writeText(text).catch(() => {});
    },
    
    // Dialogs
    MessageDialog: (options) => {
        console.log('MessageDialog called:', options);
        alert(options.message || options.Message || 'Message');
        return Promise.resolve('Ok');
    },
    
    OpenFileDialog: (options) => {
        console.log('OpenFileDialog called:', options);
        return new Promise((resolve) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = options.allowsMultipleSelection || false;
            input.accept = options.filters ? options.filters.map(f => f.pattern).join(',') : '*';
            
            input.onchange = (e) => {
                const files = Array.from(e.target.files).map(file => file.name);
                resolve(files);
            };
            
            input.click();
        });
    },
    
    SaveFileDialog: (options) => {
        console.log('SaveFileDialog called:', options);
        return Promise.resolve('mock-file-path.txt');
    }
};

// Mock Go method calls
window.go = window.go || {};

// Create mock methods for all API endpoints
const createMockMethod = (methodName) => {
    return (...args) => {
        console.log(`Mock API call: ${methodName}`, args);
        
        // Return mock data based on method name
        switch (methodName) {
            case 'GetDiaries':
                return Promise.resolve([
                    {
                        ID: '1',
                        Title: '今天的美好时光',
                        Content: '今天和你一起度过了美好的一天，记录下这个特殊的时刻...',
                        CreatedAt: new Date().toISOString(),
                        UpdatedAt: new Date().toISOString()
                    },
                    {
                        ID: '2',
                        Title: '第一次约会',
                        Content: '我们第一次约会的回忆，那个阳光明媚的下午...',
                        CreatedAt: new Date(Date.now() - 86400000).toISOString(),
                        UpdatedAt: new Date(Date.now() - 86400000).toISOString()
                    }
                ]);
                
            case 'GetAnniversaries':
                return Promise.resolve([
                    {
                        ID: '1',
                        Title: '相识纪念日',
                        Description: '我们第一次相遇的日子',
                        Date: '2023-01-01T00:00:00Z',
                        CreatedAt: new Date().toISOString(),
                        UpdatedAt: new Date().toISOString()
                    }
                ]);
                
            case 'GetQuotes':
                return Promise.resolve([
                    {
                        ID: '1',
                        Content: '爱情是生活最好的提神剂',
                        Author: '莎士比亚',
                        CreatedAt: new Date().toISOString(),
                        UpdatedAt: new Date().toISOString()
                    }
                ]);
                
            case 'GetPhotos':
                return Promise.resolve([
                    {
                        ID: '1',
                        Filename: 'photo1.jpg',
                        Description: '美好的回忆',
                        Path: '/photos/photo1.jpg',
                        CreatedAt: new Date().toISOString(),
                        UpdatedAt: new Date().toISOString()
                    }
                ]);
                
            case 'GetStatistics':
                return Promise.resolve({
                    DiaryCount: 15,
                    AnniversaryCount: 5,
                    QuoteCount: 20,
                    PhotoCount: 50,
                    TotalMemories: 90
                });
                
            case 'CreateDiary':
            case 'UpdateDiary':
                return Promise.resolve({
                    ID: Math.random().toString(36).substr(2, 9),
                    Title: args[0] || args[1] || '新日记',
                    Content: args[1] || args[2] || '日记内容',
                    CreatedAt: new Date().toISOString(),
                    UpdatedAt: new Date().toISOString()
                });
                
            case 'CreateAnniversary':
            case 'UpdateAnniversary':
                return Promise.resolve({
                    ID: Math.random().toString(36).substr(2, 9),
                    Title: args[0] || args[1] || '新纪念日',
                    Description: args[1] || args[2] || '纪念日描述',
                    Date: args[2] || args[3] || new Date().toISOString(),
                    CreatedAt: new Date().toISOString(),
                    UpdatedAt: new Date().toISOString()
                });
                
            case 'CreateQuote':
            case 'UpdateQuote':
                return Promise.resolve({
                    ID: Math.random().toString(36).substr(2, 9),
                    Content: args[0] || args[1] || '新语录',
                    Author: args[1] || args[2] || '作者',
                    CreatedAt: new Date().toISOString(),
                    UpdatedAt: new Date().toISOString()
                });
                
            case 'DeleteDiary':
            case 'DeleteAnniversary':
            case 'DeleteQuote':
            case 'DeletePhoto':
                return Promise.resolve();
                
            default:
                return Promise.resolve({});
        }
    };
};

// Create mock API methods
const apiMethods = [
    'GetDiaries', 'CreateDiary', 'UpdateDiary', 'DeleteDiary',
    'GetAnniversaries', 'CreateAnniversary', 'UpdateAnniversary', 'DeleteAnniversary',
    'GetQuotes', 'CreateQuote', 'UpdateQuote', 'DeleteQuote',
    'GetPhotos', 'UploadPhoto', 'DeletePhoto',
    'GetStatistics'
];

window.go.main = window.go.main || {};
window.go.main.App = window.go.main.App || {};

apiMethods.forEach(method => {
    window.go.main.App[method] = createMockMethod(method);
});

console.log('Wails runtime mock initialized');

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, Wails mock ready');
});
