@echo off
echo ========================================
echo 初始化 Wails 爱情个人软件项目
echo ========================================

:: 切换到项目根目录
cd /d "%~dp0\.."

:: 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: 未找到 Go 命令
    echo 请先安装 Go: https://golang.org/dl/
    pause
    exit /b 1
)

:: 检查 Wails 是否安装
where wails >nul 2>nul
if %errorlevel% neq 0 (
    echo Wails 未安装，正在安装...
    go install github.com/wailsapp/wails/v2/cmd/wails@latest
    if %errorlevel% neq 0 (
        echo 错误: Wails 安装失败
        pause
        exit /b 1
    )
    echo Wails 安装成功！
)

:: 检查 Wails 环境
echo 检查 Wails 环境...
wails doctor
if %errorlevel% neq 0 (
    echo 警告: Wails 环境检查发现问题，但可以继续
)

:: 下载 Go 依赖
echo 下载 Go 依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 下载依赖失败
    pause
    exit /b 1
)

echo ========================================
echo 初始化完成！
echo 
echo 接下来您可以：
echo 1. 运行开发服务器: scripts\dev_wails.bat
echo 2. 构建应用程序: scripts\build_wails.bat  
echo 3. 测试前端界面: scripts\test_frontend.bat
echo ========================================
pause
