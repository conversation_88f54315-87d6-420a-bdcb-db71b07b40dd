// 页面和模态框相关功能

// 扩展应用类的页面和模态框方法
Object.assign(LoveSoftwareApp.prototype, {
    // 显示模态框
    showModal(title, content, onSave = null) {
        const modalContainer = document.getElementById('modalContainer');
        if (!modalContainer) return;

        const modalId = 'modal-' + Date.now();
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = 'glass-modal active';
        
        modal.innerHTML = `
            <div class="glass-modal-content">
                <div class="glass-modal-header">
                    <h3 class="glass-modal-title">${title}</h3>
                    <button class="glass-modal-close" onclick="loveSoftwareApp.hideModal('${modalId}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="glass-modal-body">
                    ${content}
                </div>
                ${onSave ? `
                <div class="glass-modal-footer">
                    <button class="glass-btn" onclick="loveSoftwareApp.hideModal('${modalId}')">取消</button>
                    <button class="glass-btn primary" onclick="loveSoftwareApp.handleModalSave('${modalId}')">保存</button>
                </div>
                ` : ''}
            </div>
        `;

        modalContainer.appendChild(modal);
        
        // 存储保存回调
        if (onSave) {
            modal._onSave = onSave;
        }

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal(modalId);
            }
        });

        // ESC键关闭
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.hideModal(modalId);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    },

    // 隐藏模态框
    hideModal(modalId = null) {
        const modalContainer = document.getElementById('modalContainer');
        if (!modalContainer) return;

        if (modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.remove();
            }
        } else {
            modalContainer.innerHTML = '';
        }
    },

    // 处理模态框保存
    handleModalSave(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal || !modal._onSave) return;

        const form = modal.querySelector('form');
        if (form) {
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            modal._onSave(data);
        }
    },

    // 获取日记表单
    getDiaryForm(diary = null) {
        return `
            <form class="form-container">
                <div class="form-group">
                    <label class="form-label required" for="title">标题</label>
                    <input type="text" id="title" name="title" class="glass-input" 
                           placeholder="输入日记标题..." value="${diary ? diary.Title : ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label required" for="content">内容</label>
                    <textarea id="content" name="content" class="glass-textarea" 
                              placeholder="写下今天的美好时光..." required>${diary ? diary.Content : ''}</textarea>
                </div>
            </form>
        `;
    },

    // 获取日记查看视图
    getDiaryView(diary) {
        return `
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">${diary.Title}</h4>
                    <p class="card-subtitle">创建于 ${this.formatDate(diary.CreatedAt)}</p>
                </div>
                <div class="card-body">
                    <p>${diary.Content.replace(/\n/g, '<br>')}</p>
                </div>
                <div class="card-footer">
                    <small>最后更新: ${this.formatRelativeTime(diary.UpdatedAt)}</small>
                </div>
            </div>
        `;
    },

    // 获取纪念日表单
    getAnniversaryForm(anniversary = null) {
        const dateValue = anniversary ? new Date(anniversary.Date).toISOString().split('T')[0] : '';
        return `
            <form class="form-container">
                <div class="form-group">
                    <label class="form-label required" for="title">标题</label>
                    <input type="text" id="title" name="title" class="glass-input" 
                           placeholder="输入纪念日标题..." value="${anniversary ? anniversary.Title : ''}" required>
                </div>
                <div class="form-group">
                    <label class="form-label required" for="date">日期</label>
                    <input type="date" id="date" name="date" class="glass-input" 
                           value="${dateValue}" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="description">描述</label>
                    <textarea id="description" name="description" class="glass-textarea" 
                              placeholder="描述这个特殊的日子...">${anniversary ? anniversary.Description : ''}</textarea>
                </div>
            </form>
        `;
    },

    // 获取纪念日查看视图
    getAnniversaryView(anniversary) {
        return `
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">${anniversary.Title}</h4>
                    <p class="card-subtitle">${this.formatDate(anniversary.Date)}</p>
                </div>
                <div class="card-body">
                    <p>${anniversary.Description || '无描述'}</p>
                </div>
                <div class="card-footer">
                    <small>距今 ${this.calculateDaysSince(anniversary.Date)} 天</small>
                </div>
            </div>
        `;
    },

    // 获取语录表单
    getQuoteForm(quote = null) {
        return `
            <form class="form-container">
                <div class="form-group">
                    <label class="form-label required" for="content">语录内容</label>
                    <textarea id="content" name="content" class="glass-textarea" 
                              placeholder="输入触动心灵的话语..." required>${quote ? quote.Content : ''}</textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="author">作者</label>
                    <input type="text" id="author" name="author" class="glass-input" 
                           placeholder="作者姓名（可选）" value="${quote ? quote.Author : ''}">
                </div>
            </form>
        `;
    },

    // 获取语录查看视图
    getQuoteView(quote) {
        return `
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-quote-left"></i>
                        ${quote.Author || '匿名'}
                    </h4>
                    <p class="card-subtitle">收藏于 ${this.formatDate(quote.CreatedAt)}</p>
                </div>
                <div class="card-body">
                    <blockquote style="font-style: italic; font-size: 1.1em; line-height: 1.6;">
                        "${quote.Content}"
                    </blockquote>
                </div>
            </div>
        `;
    },

    // 获取照片查看视图
    getPhotoView(photo) {
        return `
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">${photo.Filename}</h4>
                    <p class="card-subtitle">上传于 ${this.formatDate(photo.CreatedAt)}</p>
                </div>
                <div class="card-body">
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <img src="${photo.Path || '/photos/' + photo.Filename}" 
                             alt="${photo.Description}" 
                             style="max-width: 100%; max-height: 400px; border-radius: 8px;">
                    </div>
                    <p>${photo.Description || '无描述'}</p>
                </div>
                <div class="card-footer">
                    <small>文件大小: ${this.formatFileSize(photo.Size || 0)}</small>
                </div>
            </div>
        `;
    },

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `glass-notification ${type} show`;
        
        const icon = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <i class="${icon}"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // 自动隐藏
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    },

    // 显示确认对话框
    showConfirm(message, onConfirm, onCancel = null) {
        this.showModal('确认', `
            <div style="text-align: center; padding: 1rem;">
                <i class="fas fa-question-circle" style="font-size: 3rem; color: var(--accent-color); margin-bottom: 1rem;"></i>
                <p style="font-size: 1.1em; margin-bottom: 2rem;">${message}</p>
                <div style="display: flex; gap: 1rem; justify-content: center;">
                    <button class="glass-btn" onclick="loveSoftwareApp.hideModal(); ${onCancel ? onCancel.toString() + '()' : ''}">取消</button>
                    <button class="glass-btn primary" onclick="loveSoftwareApp.hideModal(); ${onConfirm.toString()}()">确认</button>
                </div>
            </div>
        `);
    },

    // 显示提示对话框
    showAlert(message, type = 'info') {
        const icon = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';

        const color = {
            success: 'var(--secondary-color)',
            error: '#ef4444',
            warning: 'var(--accent-color)',
            info: 'var(--primary-color)'
        }[type] || 'var(--primary-color)';

        this.showModal('提示', `
            <div style="text-align: center; padding: 1rem;">
                <i class="${icon}" style="font-size: 3rem; color: ${color}; margin-bottom: 1rem;"></i>
                <p style="font-size: 1.1em; margin-bottom: 2rem;">${message}</p>
                <button class="glass-btn primary" onclick="loveSoftwareApp.hideModal()">确定</button>
            </div>
        `);
    }
});
