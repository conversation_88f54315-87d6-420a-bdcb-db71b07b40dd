/* 玻璃形态效果样式 */

/* 基础玻璃卡片 */
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    transition: all var(--transition-normal);
}

.glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
    transform: translateY(-2px);
}

/* 玻璃按钮 */
.glass-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.glass-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.glass-btn:active {
    transform: translateY(0);
}

.glass-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-color: var(--primary-light);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.glass-btn.primary:hover {
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
    transform: translateY(-3px);
}

.glass-btn.secondary {
    background: linear-gradient(135deg, var(--secondary-color), #7dd3fc);
    border-color: var(--secondary-color);
    color: var(--text-primary);
    box-shadow: 0 4px 15px rgba(168, 230, 207, 0.3);
}

.glass-btn.secondary:hover {
    box-shadow: 0 8px 25px rgba(168, 230, 207, 0.4);
}

.glass-btn.danger {
    background: linear-gradient(135deg, #ef4444, #f87171);
    border-color: #f87171;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.glass-btn.danger:hover {
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

/* 玻璃输入框 */
.glass-input {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-white);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    width: 100%;
}

.glass-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.glass-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* 玻璃文本域 */
.glass-textarea {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-white);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    transition: all var(--transition-normal);
    width: 100%;
    min-height: 120px;
    resize: vertical;
}

.glass-textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.glass-textarea:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

/* 玻璃选择框 */
.glass-select {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    color: var(--text-white);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    width: 100%;
    cursor: pointer;
}

.glass-select:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.1);
}

.glass-select option {
    background: var(--text-primary);
    color: var(--text-white);
}

/* 玻璃模态框 */
.glass-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.glass-modal.active {
    opacity: 1;
    visibility: visible;
}

.glass-modal-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--spacing-2xl);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: all var(--transition-normal);
}

.glass-modal.active .glass-modal-content {
    transform: scale(1) translateY(0);
}

.glass-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-modal-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-white);
}

.glass-modal-close {
    background: none;
    border: none;
    color: var(--text-white);
    font-size: var(--font-size-xl);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.glass-modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(90deg);
}

.glass-modal-body {
    color: var(--text-white);
    line-height: 1.6;
}

.glass-modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

/* 玻璃提示框 */
.glass-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-white);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    z-index: 1001;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    pointer-events: none;
}

.glass-tooltip.show {
    opacity: 1;
    visibility: visible;
}

.glass-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

/* 玻璃通知 */
.glass-notification {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    color: var(--text-white);
    max-width: 350px;
    z-index: 1002;
    transform: translateX(100%);
    transition: all var(--transition-normal);
}

.glass-notification.show {
    transform: translateX(0);
}

.glass-notification.success {
    border-left: 4px solid var(--secondary-color);
}

.glass-notification.error {
    border-left: 4px solid #ef4444;
}

.glass-notification.warning {
    border-left: 4px solid var(--accent-color);
}

.glass-notification.info {
    border-left: 4px solid #3b82f6;
}

/* 玻璃加载器 */
.glass-loader {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    color: var(--text-white);
}

.glass-loader .spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

/* 玻璃进度条 */
.glass-progress {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    height: 8px;
    overflow: hidden;
    position: relative;
}

.glass-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    transition: width var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.glass-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 响应式玻璃效果 */
@media (max-width: 768px) {
    .glass-modal-content {
        margin: var(--spacing-md);
        padding: var(--spacing-lg);
    }
    
    .glass-notification {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
        max-width: none;
    }
}
